<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36" version="26.2.15" pages="2">
  <diagram name="孪生网络架构" id="siamese-network-architecture">
    <mxGraphModel dx="2431" dy="1434" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="2336" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="VXytNBGxdB62XU7I-1el-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b1ddf0;strokeColor=#10739e;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="680" width="620" height="370" as="geometry" />
        </mxCell>
        <mxCell id="title" value="多注意力融合的Siamese网络架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="610" y="80" width="600" height="40" as="geometry" />
        </mxCell>
        <mxCell id="left-input-group" value="代码片段A输入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="420" y="140" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="left-token" value="Token特征向量&#xa;(768维)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="240" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="left-ast" value="AST特征&#xa;节点矩阵+邻接矩阵&#xa;(N×128 + N×N)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="380" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="left-cfg" value="CFG特征&#xa;基本块矩阵+邻接矩阵&#xa;(M×64 + M×M)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="540" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="left-dfg" value="DFG特征&#xa;数据节点矩阵+邻接矩阵&#xa;(K×64 + K×K)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="700" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="left-concat" value="特征拼接层&#xa;4×768→3072维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="440" y="360" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="left-projection" value="线性投影层&#xa;3072→768维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="440" y="450" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="left-encoder" value="共享编码器&#xa;3层卷积层(3×3,stride=1)&#xa;+ 2层全连接层(512维)&#xa;激活函数: ReLU&#xa;Dropout: 0.2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="400" y="540" width="240" height="100" as="geometry" />
        </mxCell>
        <mxCell id="PIbv57rk2_xOOxkdTvTi-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head1" target="attention-calc">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="head1" value="Head 1&#xa;Q,K,V: 768×96&#xa;输出: 96维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="650" y="760.36" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="PIbv57rk2_xOOxkdTvTi-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head2" target="attention-calc">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="head2" value="Head 2&#xa;Q,K,V: 768×96&#xa;输出: 96维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="770" y="760.36" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="PIbv57rk2_xOOxkdTvTi-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head3" target="attention-calc">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="910" y="870" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="head3" value="Head 3&#xa;Q,K,V: 768×96&#xa;输出: 96维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="890" y="760.36" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="head-dots" value="..." style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1010" y="780.36" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PIbv57rk2_xOOxkdTvTi-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head8" target="attention-calc">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="910" y="870" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="head8" value="Head 8&#xa;Q,K,V: 768×96&#xa;输出: 96维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1070" y="760.36" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="PIbv57rk2_xOOxkdTvTi-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="attention-calc" target="head-concat">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="attention-calc" value="注意力权重计算&#xa;Attention(Q,K,V) = softmax(QK^T/√d)V&#xa;d = 96 (每个头的维度)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="770" y="880" width="280" height="60" as="geometry" />
        </mxCell>
        <mxCell id="head-concat" value="头部拼接&#xa;8×96→768维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="830" y="980" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="residual-norm" value="残差连接 + 层归一化&#xa;输出 = LayerNorm(输入 + 注意力输出)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="790" y="1070" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="right-input-group" value="代码片段B输入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1200.44" y="140" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="right-token" value="Token特征向量&#xa;(768维)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1020.44" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="right-ast" value="AST特征&#xa;节点矩阵+邻接矩阵&#xa;(N×128 + N×N)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1160.44" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="right-cfg" value="CFG特征&#xa;基本块矩阵+邻接矩阵&#xa;(M×64 + M×M)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1320.44" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="right-dfg" value="DFG特征&#xa;数据节点矩阵+邻接矩阵&#xa;(K×64 + K×K)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1480.44" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="right-concat" value="特征拼接层&#xa;4×768→3072维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1220.44" y="360" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="right-projection" value="线性投影层&#xa;3072→768维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1220.44" y="450" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="right-encoder" value="共享编码器&#xa;(权重共享)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1180.44" y="540" width="240" height="100" as="geometry" />
        </mxCell>
        <mxCell id="similarity-calc" value="相似度计算层&lt;br&gt;cos_sim = (A·B)/(||A||×||B||)&lt;br&gt;eucl_dist = ||A-B||₂" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="790" y="1180" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="output" value="相似度分数&#xa;[0, 1]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="840" y="1310" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="edge1" parent="1" source="left-token" target="left-concat" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge2" parent="1" source="left-ast" target="left-concat" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge3" parent="1" source="left-cfg" target="left-concat" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge4" parent="1" source="left-dfg" target="left-concat" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge5" parent="1" source="left-concat" target="left-projection" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge6" parent="1" source="left-projection" target="left-encoder" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge7" parent="1" source="right-token" target="right-concat" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge8" parent="1" source="right-ast" target="right-concat" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge9" parent="1" source="right-cfg" target="right-concat" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge10" parent="1" source="right-dfg" target="right-concat" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge11" parent="1" source="right-concat" target="right-projection" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge12" parent="1" source="right-projection" target="right-encoder" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge13" style="" parent="1" target="head1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="640" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge14" style="exitX=1;exitY=1;exitDx=0;exitDy=0;" parent="1" target="head2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="640" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge15" parent="1" target="head3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="640" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge16" style="exitX=1;exitY=1;exitDx=0;exitDy=0;" parent="1" target="head8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="640" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge17" style="exitX=0;exitY=1;exitDx=0;exitDy=0;" parent="1" source="right-encoder" target="head1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1390" y="619.9985542168678" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge18" style="exitX=0;exitY=1;exitDx=0;exitDy=0;" parent="1" source="right-encoder" target="head2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1390" y="625.0078873239439" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge19" style="exitX=0;exitY=1;exitDx=0;exitDy=0;" parent="1" source="right-encoder" target="head3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1390" y="632.0549152542375" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge20" style="exitX=0;exitY=1;exitDx=0;exitDy=0;" parent="1" source="right-encoder" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1250.44" y="640.36" as="sourcePoint" />
            <mxPoint x="1010.0023069001031" y="760.3600000000001" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge25" parent="1" source="head-concat" target="residual-norm" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge26" parent="1" source="residual-norm" target="similarity-calc" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge27" parent="1" source="similarity-calc" target="output" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="weight-sharing" value="权重共享" style="text;html=1;strokeColor=#d6b656;fillColor=#fff2cc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="870" y="590" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sharing-line" value="" style="endArrow=none;dashed=1;html=1;strokeColor=#d6b656;strokeWidth=3;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" edge="1" target="right-encoder">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="640" y="590" as="sourcePoint" />
            <mxPoint x="1340" y="590" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="legend-title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend-input" value="输入特征" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="50" y="80" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend-process" value="处理层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="50" y="120" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend-encoder" value="编码器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="50" y="160" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend-attention" value="注意力头" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="50" y="200" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend-similarity" value="相似度计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="50" y="240" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VXytNBGxdB62XU7I-1el-2" value="多头注意力融合模块" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="770" y="680" width="240" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="孪生网络架构 的副本" id="wbVr8c4dKu22Z_RBckFx">
    <mxGraphModel dx="3178" dy="4211" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="2336" math="0" shadow="0">
      <root>
        <mxCell id="wOE2_XxtT6oTuW1CAiUu-0" />
        <mxCell id="wOE2_XxtT6oTuW1CAiUu-1" parent="wOE2_XxtT6oTuW1CAiUu-0" />
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-0" value="基于改进孪生网络的特征融合与相似度测量架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="500" y="-450" width="600" height="40" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-2" target="7I5rGI9R9hLFKKE-l2Ty-49">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-2" value="代码片段A输入(漏洞片段)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="510" y="-230" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-3" value="Token特征向量&#xa;(768维)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="510" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-4" value="AST特征&#xa;节点矩阵+邻接矩阵&#xa;(N×128 + N×N)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="650" y="80" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-5" value="CFG特征&#xa;基本块矩阵+邻接矩阵&#xa;(M×64 + M×M)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="810" y="80" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-6" value="DFG特征&#xa;数据节点矩阵+邻接矩阵&#xa;(K×64 + K×K)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="970" y="80" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-7" value="特征拼接层&#xa;4×768→3072维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="710" y="200" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-8" value="线性投影层&#xa;3072→768维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="710" y="310" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-9" value="共享编码器&#xa;3层卷积层(3×3,stride=1)&#xa;+ 2层全连接层(512维)&#xa;激活函数: ReLU&#xa;Dropout: 0.2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="670" y="410" width="240" height="100" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-10" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="490" y="600" width="620" height="380" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-12" target="7I5rGI9R9hLFKKE-l2Ty-20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-12" value="Head 1&#xa;Q,K,V: 768×96&#xa;输出: 96维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="545" y="670" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-14">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="800" y="820" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-14" value="Head 2&#xa;Q,K,V: 768×96&#xa;输出: 96维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="665" y="670" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-16">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="800" y="820" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-16" value="Head 3&#xa;Q,K,V: 768×96&#xa;输出: 96维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="785" y="670" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-17" value="..." style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="905" y="690" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-19">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="800" y="820" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-19" value="Head 8&#xa;Q,K,V: 768×96&#xa;输出: 96维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="965" y="670" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-20" value="注意力权重计算&#xa;Attention(Q,K,V) = softmax(QK^T/√d)V&#xa;d = 96 (每个头的维度)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="660" y="820" width="280" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-21" value="头部拼接&#xa;8×96→768维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="720" y="910" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-22" value="残差连接 + 层归一化&#xa;输出 = LayerNorm(输入 + 注意力输出)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="680" y="1010" width="240" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-24" target="7I5rGI9R9hLFKKE-l2Ty-49">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-24" value="代码片段B输入（检测片段）" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="910" y="-230" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-25" value="相似度计算层&#xa;余弦相似度 + 欧几里得距离&#xa;cos_sim = (A·B)/(||A||×||B||)&#xa;eucl_dist = ||A-B||₂" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="680" y="1130" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-26" value="相似度分数&#xa;[0, 1]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=14;fontStyle=1;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="730" y="1260" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-27" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-3" target="7I5rGI9R9hLFKKE-l2Ty-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-28" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-4" target="7I5rGI9R9hLFKKE-l2Ty-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-29" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-5" target="7I5rGI9R9hLFKKE-l2Ty-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-30" style="exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-6" target="7I5rGI9R9hLFKKE-l2Ty-7">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="974.7826086956525" y="140" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-31" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-7" target="7I5rGI9R9hLFKKE-l2Ty-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-32" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-8" target="7I5rGI9R9hLFKKE-l2Ty-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-33" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-9" target="7I5rGI9R9hLFKKE-l2Ty-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-34" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-9" target="7I5rGI9R9hLFKKE-l2Ty-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-35" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-9" target="7I5rGI9R9hLFKKE-l2Ty-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-36" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-9" target="7I5rGI9R9hLFKKE-l2Ty-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-37" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-21" target="7I5rGI9R9hLFKKE-l2Ty-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-38" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-22" target="7I5rGI9R9hLFKKE-l2Ty-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-39" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-25" target="7I5rGI9R9hLFKKE-l2Ty-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-40" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="560" y="-400" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-41" value="输入特征" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="560" y="-360" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-42" value="处理层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="660" y="-360" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-43" value="编码器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="760" y="-360" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-44" value="注意力头" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="860" y="-360" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-45" value="相似度计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="960" y="-360" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-49" target="7I5rGI9R9hLFKKE-l2Ty-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-49" target="7I5rGI9R9hLFKKE-l2Ty-4">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="805" y="30" />
              <mxPoint x="720" y="30" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-49" target="7I5rGI9R9hLFKKE-l2Ty-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-49" value="数据预处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;fontSize=14;fontStyle=1;fontColor=#ffffff;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="630" y="-90" width="350" height="70" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-50" value="&lt;span style=&quot;font-size: 14px; font-weight: 700;&quot;&gt;多头注意力融合模块&amp;nbsp;&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="wOE2_XxtT6oTuW1CAiUu-1">
          <mxGeometry x="490" y="620" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7I5rGI9R9hLFKKE-l2Ty-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.552;entryY=0.037;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="wOE2_XxtT6oTuW1CAiUu-1" source="7I5rGI9R9hLFKKE-l2Ty-49" target="7I5rGI9R9hLFKKE-l2Ty-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
